<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mb-2 mt-1 mx-auto" [formGroup]="FilterForm">
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getPendingApprovalData()"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>

                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlPatients" class="form-control custom-control" formControlName="ddlPatients"
                                        (change)="getPendingApprovalData()"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPatients'].errors}">
                                        <option value="">---Select Patients---</option>
                                        <option value="0">My Patients</option>
                                        <option value="1">Hidden Patients</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-2 mb-1 mx-auto">
                                <div class="col-10 col-md-10 px-1 py-1">
                                    <input type="text" class="form-control small" maxlength="1000"
                                        (keyup)="search(searchByName)"
                                        placeholder="Search for Patient Name, Account No or Room" aria-label="Search"
                                        aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                        [ngModelOptions]="{standalone: true}">
                                    <img alt=' ' class="search position-absolute mr-3"
                                        src="../../../assets/img/search.png">
                                </div>
                                <div class="col-2 col-md-2 col-md-2 px-1 py-1">
                                    <img alt=' ' *ngIf="listOfPatients.length>0" [src]="img1" class="downloadIcon"
                                        (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" title="Export Excel">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div *ngIf="is_check_box_selected && userAccess.physicianModuleAccess=='YES'"
                    class="col-12 col-md-2 px-1 py-1">
                    <button class="btn btn-outline-info px-2 btn-block" data-bs-toggle="modal"
                        (click)="approveEncounter()">Approve</button>
                </div>
                <!-- table Card starts -->
                <div class="card-body px-md-2 py-md-1 px-1 py-0">
                    <app-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/physician/pending-approval-encounters'"  [FilterForm]="FilterForm" [totalCount]="0" [searchByName]="searchByName" [device]="device" (enventUpdateSortObj)="updateSortObj($event)"  [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"  (eventListOfPatients)="getPendingApprovalData()" (eventChkAllForApprove)="chkAllForApprove($event)" (eventChkHaveanyToApprove)="chkHaveanyToApprove()" [userAccess]="userAccess" [isSelectAllChecked]="isSelectAllChecked"></app-patients>
                </div>
                <!-- table card ends-->

            </div>
        </div>

    </div>
</div>
