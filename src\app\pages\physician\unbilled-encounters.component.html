<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0" [formGroup]="FilterForm">
                            <div class="row mt-2 mb-1 mx-auto">
                                <div class="col-12 col-md-6 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        (change)="getUnBilledEncounters()"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option value="All">All Facilities</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-6 px-1 py-1">
                                    <input type="text" class="form-control small" maxlength="1000"
                                        (keyup)="search(searchKey)"
                                        placeholder="Search for Account No#,Patient Name"
                                        aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="searchKey"
                                        [ngModelOptions]="{standalone: true}">
                                    <img alt=' ' class="search position-absolute mr-3"
                                        src="../../../assets/img/search.png">
                                </div>
                                <div *ngIf="device" class="col-12 col-md-2 text-right text-md-left px-1 py-1">
                                    <button type="button" class="btn btn-outline-info px-2 btn-block"
                                        (click)="toggleSideNav('sort')">
                                        <i class="fas fa-sort"></i> Sort
                                    </button>
                                </div>
                                <div *ngIf="device && hideSideNav && toggleType == 'sort'"
                                    class="col-12 col-md-2 px-1 py-1">
                                    <nav id="sidebar"
                                        class="navbar-light bg-light d-inline-block d-lg-none d-md-none w-100">
                                        <div class="m-auto text-right">
                                            <button type="button"
                                                class="navbar-toggler btn btn-outline-secondary text-right"
                                                (click)="toggleSideNav('close')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div>
                                            <div class="row mx-auto text-secondary">
                                                <div class="col mb-2">
                                                    <div class="form-group my-auto">
                                                        <label class="my-auto" for="sortColumn">Sort By</label>
                                                        <select name="sortOrder"
                                                            class="rounded-inputs20 select-select form-control bg-secondary text-white"
                                                            (change)="onChangeForSort($any($event.target).value)"
                                                            id="sortColumn">
                                                            <option *ngFor="let sortOption of sortOptions"
                                                                [value]="sortOption.id"
                                                                [selected]="sortOption.id == sortColumnBy">
                                                                {{sortOption.name}}</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mt-3 mb-auto">
                                                        <label class="my-auto" for="sortOrder">Sort Order</label>
                                                        <select name="sortOption"
                                                            class="rounded-inputs20 select-select form-control bg-secondary text-white"
                                                            (change)="onChangeForSortOrder($any($event.target).value)"
                                                            id="sortOrder">
                                                            <option *ngFor="let sortOrder of sortOrders"
                                                                [value]="sortOrder" [selected]="sortOrder == orderBy">
                                                                {{sortOrder}}</option>

                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </nav>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- table Card starts -->
                <div class="card-body px-md-2 py-md-1 px-1 py-0">
                    <div class="row mx-0">
                        <div class="col-12 px-0">
                            <!-- table start -->
                            <div *ngIf="!device">
                                <table class="table mb-0 customDataTable width-100per" border="1">
                                    <thead>
                                        <tr>

                                            <th class="text-center pb-0 cursor-pointer"
                                                (keyup)="sortColumn('account_Number')"
                                                (click)="sortColumn('account_Number')">
                                                Account No#
                                                <span class="float-right" tooltip="Sort By Account#"
                                                    triggers="mouseenter mouseleave click">
                                                    <i *ngIf="sortColumnBy == 'account_Number' && orderByAccount == 'desc'"
                                                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy == 'account_Number' && orderByAccount == 'asc'"
                                                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy != 'account_Number'"
                                                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                                </span>
                                            </th>

                                            <th class="text-center pb-0 cursor-pointer"
                                                (keyup)="sortColumn('patient_Name')"
                                                (click)="sortColumn('patient_Name')">
                                                Patient Name
                                                <span class="float-right" tooltip="Sort By Patient"
                                                    triggers="mouseenter mouseleave click">
                                                    <i *ngIf="sortColumnBy == 'patient_Name' && orderByPatient == 'desc'"
                                                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy == 'patient_Name' && orderByPatient == 'asc'"
                                                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy != 'patient_Name'"
                                                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                                </span>
                                            </th>

                                            <th class="text-center pb-0 cursor-pointer"
                                                (keyup)="sortColumn('facility_Name')"
                                                (click)="sortColumn('facility_Name')">
                                                Facility
                                                <span class="float-right" tooltip="Sort By Facility"
                                                    triggers="mouseenter mouseleave click">
                                                    <i *ngIf="sortColumnBy == 'facility_Name' && orderByFacility == 'desc'"
                                                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy == 'facility_Name' && orderByFacility == 'asc'"
                                                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy != 'facility_Name'"
                                                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                                </span>
                                            </th>
                                            <th class="text-center pb-0 cursor-pointer"
                                                (keyup)="sortColumn('encounterseendate_sort')"
                                                (click)="sortColumn('encounterseendate_sort')">
                                                Missed Encounter Seen Date
                                                <span class="float-right" tooltip="Sort By Encounter Seen Date"
                                                    triggers="mouseenter mouseleave click">
                                                    <i *ngIf="sortColumnBy == 'encounterseendate_sort' && orderByencounterseendate == 'desc'"
                                                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy == 'encounterseendate_sort' && orderByencounterseendate == 'asc'"
                                                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy != 'encounterseendate_sort'"
                                                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                                </span>
                                            </th>
                                            <th class="text-center pb-0 cursor-pointer"
                                                (keyup)="sortColumn('admission_Date_sort')"
                                                (click)="sortColumn('admission_Date_sort')">
                                                Admission Date
                                                <span class="float-right" tooltip="Sort By Encounter Seen Date"
                                                    triggers="mouseenter mouseleave click">
                                                    <i *ngIf="sortColumnBy == 'admission_Date_sort' && orderByadmit_Datetime == 'desc'"
                                                        class="fas fa-sort-alpha-up float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy == 'admission_Date_sort' && orderByadmit_Datetime == 'asc'"
                                                        class="fas fa-sort-alpha-down float-right line-height-1 text-info"></i>
                                                    <i *ngIf="sortColumnBy != 'admission_Date_sort'"
                                                        class="fas fa-exchange-alt float-right line-height-1 text-white"></i>
                                                </span>
                                            </th>

                                            <th class="text-center pb-0">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container
                                            *ngFor="let item of listOfPatients | paginate: {itemsPerPage:10, currentPage: p}; let i = index;">
                                            <tr *ngIf="item.account_Number!=''">
                                                <td data-th="Account No#" class="text-left text-md-center pb-0">
                                                    <span class="mobile-text"><button class="btn btn-link"
                                                            [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                                                            placement="right" triggers="click" [outsideClick]="true">
                                                            {{item.account_Number}}
                                                        </button></span>
                                                    <ng-template #popTemplate>
                                                        <div class="accordion-inner">
                                                            <ul class="my-auto">
                                                                <li class="text-left list-style-type-none btn-sm my-1">
                                                                    <a class="deletepointer"
                                                                        [routerLink]="['/physician/start-new-encounter']"
                                                                        [state]="{patient:item,encounterSeenDate:item.encounterseendate,backUrl:'/physician/unbilled-encounters',facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchKey,ddlFacility:FilterForm.value.ddlFacility}}">
                                                                        Start New Encounter</a></li>
                                                            </ul>
                                                        </div>
                                                    </ng-template>

                                                </td>
                                                <td data-th="Patient Name" class="text-left text-md-center pb-0">
                                                    <span class="mobile-text"><button class="btn btn-link"
                                                            [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                                                            placement="right" triggers="click" [outsideClick]="true">
                                                            {{item.patient_Name}}
                                                        </button></span>
                                                </td>
                                                <td data-th="Facility" class="text-left text-md-center pb-0"><span
                                                        class="mobile-text">{{item.facility_Name}}</span></td>
                                                <td data-th="Missed Encounter Seen Date"
                                                    class="text-left text-md-center pb-0"><span
                                                        class="mobile-text">{{item.encounterseendate}}</span></td>
                                                <td data-th="Admission Date" class="text-left text-md-center pb-0"><span
                                                        class="mobile-text">{{item.admit_Datetime | date: 'MM/dd/yyyy
                                                        hh:mm:ss a'}}</span></td>
                                                <td data-th="Actions" class="text-left text-md-center pb-0"><span
                                                        class="mobile-text">
                                                        <a class="mx-1" title="Remove"
                                                            (click)='openDeleteConfirmPopup(item)'><i
                                                                class="far fa-trash-alt text-danger"></i></a>
                                                    </span></td>
                                            </tr>
                                        </ng-container>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="11" class="m-0 p-0" style="background: white !important;">
                                                <pagination-controls previousLabel="" nextLabel=""
                                                    (pageChange)="p = $event">
                                                </pagination-controls>
                                            </td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <!-- table end -->
                            <!-- mobile start-->
                            <div *ngIf="device">
                                <ng-container
                                    *ngFor="let item of listOfPatients | paginate: { id:'patient_ID',itemsPerPage:10, currentPage: p}; let i = index;">
                                    <div
                                        class="row border-blue bg-white my-1 shadow border mx-auto mobile text-secondary">
                                        <div class="col p-1 font-weight-bold cursor-pointer small"
                                            [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                                            placement="right" triggers="click" [outsideClick]="true">
                                            <h5 class="h5 my-auto"
                                                [ngClass]="{'hasTodayEncounter':(item.hasPreviousEncounter=='1' && item.resetPriorEncounterStatus=='1'),'hasDraftEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID!='0' && item.encounteR_ID!=null),'hasPriorEncounter':(item.hasPreviousEncounter=='1' && item.encounteR_ID==null && item.resetPriorEncounterStatus==null),'hasNoEncounter':(item.hasPreviousEncounter=='0')}">
                                                {{item.patient_Name}}</h5>
                                            <div class="d-inline-block pb-1 pr-md-2 pr-1 text-left m-auto">
                                                <h6 class="card-title small my-auto">
                                                    <span>{{item.account_Number}}</span>
                                                </h6>
                                            </div>
                                            <div class="d-inline-block pb-1 pr-md-2 pr-1 text-left m-auto">
                                                <h6 class="card-title small my-auto">
                                                    <span
                                                        class="bg-blue text-white px-1 rounded border">{{item.facility_Name}}</span>
                                                </h6>
                                            </div>
                                        </div>
                                        <div class="col-5 my-auto p-1 small border border-blue border-top-0 border-bottom-0 cursor-pointer"
                                            [popover]="popTemplate" popoverTitle="{{item.patient_Name}}"
                                            placement="left" triggers="click" [outsideClick]="true">

                                            <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                                                <h6 class="card-title small my-auto">Missed Encounter Seen Date:
                                                    <span>{{item.encounterseendate}}</span></h6>
                                            </div>

                                            <div class="d-inline-block pr-md-2 pr-1 text-left m-auto">
                                                <h6 class="card-title small my-auto">Admission Date:
                                                    <span>{{item.admit_Datetime | date: 'MM/dd/yyyy hh:mm:ss a'}}</span>
                                                </h6>
                                            </div>
                                        </div>
                                        <div class="col h6 my-auto p-1 font-weight-bold">
                                            <div class="row mx-auto">
                                                <div class="col px-0 mx-auto text-center">
                                                    <div class="p-0">
                                                        <a class="mx-1" title="Remove"
                                                            (click)='openDeleteConfirmPopup(item)'><i
                                                                class="far fa-trash-alt text-danger"></i></a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <ng-template #popTemplate>
                                        <div class="accordion-inner">
                                            <ul class="my-auto">
                                                <li class="text-left list-style-type-none btn-sm my-1">
                                                    <a class="deletepointer"
                                                        [routerLink]="['/physician/start-new-encounter']"
                                                        [state]="{patient:item,encounterSeenDate:item.encounterseendate,backUrl:'/physician/unbilled-encounters',facilityType:item.isPrimeFacility,filterObj:{p:p,searchByName:searchKey,ddlFacility:FilterForm.value.ddlFacility}}">
                                                        <span *ngIf='item.encounterid ==0'> Start New
                                                            Encounter</span><span *ngIf='item.encounterid !=0'> My
                                                            Encounter</span></a>
                                                </li>
                                            </ul>
                                        </div>
                                    </ng-template>

                                </ng-container>
                                <div class="row mx-auto">
                                    <div class="col-12">
                                        <pagination-controls previousLabel="" nextLabel="" (pageChange)="p = $event">
                                        </pagination-controls>
                                    </div>
                                </div>
                            </div>
                            <!-- mobile end-->
                        </div>
                    </div>
                </div>
                <!-- table card ends-->

            </div>
        </div>

    </div>