<!-- Begin Page Content Starts -->
<div class="container-fluid">
    <!-- Content Row -->
    <div class="row">
        <!-- Area Chart -->
        <div class="col px-0">
            <div class="card shadow border-blue">
                <!-- Card Header - Dropdown -->
                <div class="card-header-new p-2" style="background: #0169ab;" [formGroup]="FilterForm">
                    <div class="align-items-center row mx-auto">
                        <div class="col-12 px-0">

                            <div class="row mb-2 mt-1 mx-auto">
                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlFacility" class="form-control" formControlName="ddlFacility"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlFacility'].errors}">
                                        <option value="">---Select Facility---</option>
                                        <option [value]="s.facilityName" *ngFor="let s of listOfFacilities">
                                            <span>{{s.facilityName}}</span>
                                        </option>
                                    </select>

                                </div>

                                <div class="col-12 col-md-5 px-1 py-1">
                                    <select id="ddlPatients" class="form-control custom-control"
                                        formControlName="ddlPatients"
                                        [ngClass]="{ 'is-invalid': submitted && f['ddlPatients'].errors}">
                                        <option value="">---Select Patients---</option>
                                        <option value="0">My Patients</option>
                                        <option value="1">Hidden Patients</option>
                                    </select>
                                </div>
                                <div class="col-12 col-md-2 text-right text-md-left px-1 py-1">
                                    <button title="Submit Filter" class="btn btn-outline-info px-2 btn-block"
                                        (click)="FilterPatientDetails()" type="submit">
                                        <i class="fa-filter fas font-size-13"></i>
                                        Filter
                                    </button>
                                </div>
                                <div *ngIf="device" class="col-12 col-md-2 text-right text-md-left px-1 py-1">
                                    <button type="button" class="btn btn-outline-info px-2 btn-block"
                                        (click)="toggleSideNav('sort')">
                                        <i class="fas fa-sort"></i> Sort
                                    </button>
                                </div>
                                <div *ngIf="device && hideSideNav && toggleType == 'sort'"
                                    class="col-12 col-md-2 px-1 py-1">
                                    <nav id="sidebar"
                                        class="navbar-light bg-light d-inline-block d-lg-none d-md-none w-100">
                                        <div class="m-auto text-right">
                                            <button type="button"
                                                class="navbar-toggler btn btn-outline-secondary text-right"
                                                (click)="toggleSideNav('close')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div>
                                            <div class="row mx-auto text-secondary">
                                                <div class="col mb-2">
                                                    <div class="form-group my-auto">
                                                        <label class="my-auto" for="sortColumn">Sort By</label>
                                                        <select name="sortOrder"
                                                            class="rounded-inputs20 select-select form-control bg-secondary text-white"
                                                            (change)="onChangeForSort($any($event.target).value)"
                                                            id="sortColumn">
                                                            <option *ngFor="let sortOption of sortOptions"
                                                                [value]="sortOption.id"
                                                                [selected]="sortOption.id == sortColumnBy">
                                                                {{sortOption.name}}</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group mt-3 mb-auto">
                                                        <label class="my-auto" for="sortOrder">Sort Order</label>
                                                        <select name="sortOption"
                                                            class="rounded-inputs20 select-select form-control bg-secondary text-white"
                                                            (change)="onChangeForSortOrder($any($event.target).value)"
                                                            id="sortOrder">
                                                            <option *ngFor="let sortOrder of sortOrders"
                                                                [value]="sortOrder" [selected]="sortOrder == orderBy">
                                                                {{sortOrder}}</option>

                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </nav>
                                </div>
                            </div>
                            <div class="row mt-2 mb-1 mx-auto">
                                <div class="col-10 col-md-10 px-1 py-1">
                                    <input type="text" class="form-control small" maxlength="1000"
                                        placeholder="Search for Patient Name, Account No, MRN or Room"
                                        aria-label="Search" aria-describedby="basic-addon2" [(ngModel)]="searchByName"
                                        [ngModelOptions]="{standalone: true}">
                                    <img alt=' ' class="search position-absolute mr-3"
                                        src="../../../assets/img/search.png" (click)="onKeyPatientSearch()">
                                </div>
                                <div class="col-2 col-md-2 col-md-2 px-1 py-1">
                                    <img alt=' ' *ngIf="listOfPatients.length>0" src=".././../../assets/img/excel.png" class="downloadIcon"
                                        (keyup)="exportAsXLSX()" (click)="exportAsXLSX()" title="Export Excel">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <!-- table Card starts -->
                <div class="card-body px-md-2 py-md-1 px-1 py-0">
                    <app-patients [listOfPatients]="listOfPatients" [p]="p" [pageName]="'/physician/my-patients'" [FilterForm]="FilterForm" [totalCount]="messageCount" [searchByName]="searchByName" [device]="device" (enventUpdateSortObj)="updateSortObj($event)"  [orderBy]="orderBy" [sortColumnBy]="sortColumnBy"  (eventListOfPatients)="childListOfPatients($event)" (eventRemovePatient)="RemovePatient($event)"></app-patients>
                </div>
                <!-- table card ends-->

            </div>
        </div>

    </div>
</div>